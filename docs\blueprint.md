# **App Name**: GameDev Folio

## Core Features:

- About Me Section: A section detailing personal background and objectives.
- Skills Display: Display skills organized by categories such as front end, back end, tools, and game development.
- Experience Showcase: Divide experience into Jobs, Products, and Reviews, displaying details for each.
- Contact Section: Links to social media accounts for contact.
- Profile Picture Integration: Incorporate the game developer's profile picture into the header for a personal touch.

## Style Guidelines:

- Primary color: Soft purple (#B19CD9) to convey creativity and calmness.
- Background color: Very light purple (#F0F0F8) for a comfortable and unobtrusive backdrop.
- Accent color: Light pink (#FFB6C1) to complement the purple and enhance the cute aesthetic.
- Body and headline font: 'Poppins' (sans-serif) for a modern, geometric look. Note: currently only Google Fonts are supported.
- Utilize cute and friendly icons that align with the various sections of the portfolio. All icons should use the selected analogous color scheme of soft purples and light pinks.
- Employ a fluid and responsive layout to ensure the portfolio looks great on any device, emphasizing readability and user experience. Use soft shadows and rounded corners on blocks.
- Incorporate subtle animations such as fade-ins, parallax scrolling, or hover effects to add a touch of playfulness without overwhelming the user.